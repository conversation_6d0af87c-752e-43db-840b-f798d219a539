<!-- Template: {{ _self }} -->

<div class="section section--article bg-alternative">
    <div class="container">
        <article class="media-article media-article--reverse">
            <div class="media-article__content">
                <p>{{ entry.subtitle }}</p>
                <h2>{{ entry.itemTitle }}</h2>
                {{ entry.richtext }}

                {% if entry.featureList|length %}
                <div class="media-article__features">
                    {% for feature in entry.featureList.all() %}
                    <div class="media-article__feature">
                        <h3>{{ feature.featureTitle }}</h3>
                        <p>{{ feature.featureDescription }}</p>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% if entry.linkHelper|length %}
                <div class="media-article__actions">
                    {% include './01_core/_blocks/link-helper.twig' with {
                        'linkHelper': {
                            modifierClass: 'button--alt'
                        }
                    } %}
                </div>
                {% endif %}
            </div>
            <img src="{{ entry.imageSingular.one().getUrl({ mode: 'crop', width: 800, height: 600 }) }}" alt="{{ entry.title }}" loading="lazy" />
        </article>
    </div>
</div>

