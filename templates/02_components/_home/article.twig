<!-- Template: {{ _self }} -->

{% set subTopic = entry.subTopic.all() ?? null %}

<div class="section section--article bg-alternative">
    <div class="container">
        <article class="media-article media-article--reverse">
            <div class="media-article__content">
                <p>{{ entry.subtitle }}</p>
                <h2>{{ entry.itemTitle }}</h2>
                {{ entry.richtext }}
                <div class="media-article__features">
                    {% for topic in subTopic %}
                        <div class="media-article__feature">
                            <h3>{{ topic.title }}</h3>
                            {{ topic.richtextBasic }}
                        </div>
                    {% endfor %}
                </div>
       

                {% if entry.linkHelper|length %}
                <div class="media-article__actions">
                    {% include './01_core/_blocks/link-helper.twig' with {
                        'linkHelper': {
                            modifierClass: 'button--transparent'
                        }
                    } %}
                </div>
                {% endif %}
            </div>
            <img src="{{ entry.imageSingular.one().getUrl({ mode: 'crop', width: 800, height: 600 }) }}" alt="{{ entry.title }}" loading="lazy" />
        </article>
    </div>
</div>

