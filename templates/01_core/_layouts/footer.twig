<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as macros %}
{% import "01_core/_macros/macros-assets.twig" as assetMacros %}

{% set smallLinks = craft.entries.section('footer').type('quicklinks').slug('small-links').one() %}
{% set associations = craft.entries.section('footer').type('associations').one() %}

<footer id="footer" class="footer">
    <div class="footer__body section">
        <div class="container footer__endcap__container">
            <div class="footer__text">
                <p>Copyright &copy; {{ macros.yearTillCurrent(2023) }}
                    {% if siteIdentity.companyName %}
                    {{ siteIdentity.companyName }}
                    {% else %}
                    {{ siteName }}
                    {% endif %}
                </p>
                {% if siteIdentity.abn ?? false %}
                    <p>ABN: {{ siteIdentity.abn }}</p>
                {% endif %}
            </div>
            <div class="footer__text">

                {#
                Association logos
                 #}

                {% if associations ?? false %}
                <ul class="associations">
                    {% for entry in associations.organisations.all() %}
                    <li class="associations__item">

                        {% set entityLink = entry.linkHelper.one() ?? false ? macros.linkHelperLink(entry.linkHelper.one()) : null %}
                        {% tag entityLink ? 'a' : 'picture' with {
                            class: 'associations__picture',
                            href: entityLink ?? null,
                            target: entityLink ? '_blank' : null
                        } %}

                            <span class="-vis-hidden">{{ entry.itemTitle }}</span>

                            {% set image = entry.imageSingular.one() ?? null %}
                            {% if image %}
                                {{ assetMacros.vectorSwitcher(image, 200, 200 ) }}
                            {% endif %}

                        {% endtag %}

                    </li>
                    {% endfor %}
                </ul>
                {% endif %}

                {% if smallLinks ?? false %}
                    <ul class="footer__technical-links">
                        {% include './01_core/_blocks/link-helper.twig' with { entry: smallLinks, itemParentElement : 'li' } %}
                    </ul>
                {% endif %}
                
                {% if smallLinks ?? false %}
                    <ul class="footer__technical-links">
                        {% include './01_core/_blocks/social-link.twig' with { socialLinksEntries: smallLinks.socialLinks.all(), itemParentElement : 'li' } %}
                    </ul>
                {% endif %}

            </div>
        </div>
    </div>

    {% set enews = craft.entries.section('footer').type('enews').one() ?? null %}
    {% include 'patterns/02-components/enews-signup/enewsSignup.twig' %}

    {# <div class="footer__endcap">
        <div class="container footer__body__container">
            {% for entry in craft.entries.section('footer').type('quicklinks').limit(3).slug('not small-links').all() %}
                <div class="footer__quicklinks">
                    <h3>{{ entry.itemTitle }}</h3>
                    {% if entry.linkHelper|length %}
                        <ul class="footer__quicklinks__list">
                            {% include './01_core/_blocks/link-helper.twig' with { itemParentElement : 'li' } %}
                        </ul>
                    {% endif %}
                </div>
            {% endfor %}
            {% include '02_components/contactDetails' with { title: 'Contact Us'} %}
            </div>
        </div>
    </div> #}
    {% include 'patterns/02-components/footer/website-attribution.twig' with {
        socialLinksEntries: smallLinks.socialLinks.all(),
        smallLinks: smallLinks
    } %}
</footer>
