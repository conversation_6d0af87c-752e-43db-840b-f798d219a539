<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as macros %}
{% import "01_core/_macros/macros-assets.twig" as assetMacros %}

{% set smallLinks = craft.entries.section('footer').type('quicklinks').slug('small-links').one() %}
{% set associations = craft.entries.section('footer').type('associations').one() %}
{% set enews = craft.entries.section('footer').type('enews').one() ?? null %}

<footer id="footer" class="footer">
    <div class="footer__body section">
        <div class="container footer__endcap__container">
            <div class="footer__text">
                <p>Copyright &copy; {{ macros.yearTillCurrent(2023) }}
                    {% if siteIdentity.companyName %}
                    {{ siteIdentity.companyName }}
                    {% else %}
                    {{ siteName }}
                    {% endif %}
                </p>
                {% if siteIdentity.abn ?? false %}
                    <p>ABN: {{ siteIdentity.abn }}</p>
                {% endif %}
            </div>
            <div class="footer__text">

                {#
                Association logos
                 #}

                {% if associations ?? false %}
                <ul class="associations">
                    {% for entry in associations.organisations.all() %}
                    <li class="associations__item">

                        {% set entityLink = entry.linkHelper.one() ?? false ? macros.linkHelperLink(entry.linkHelper.one()) : null %}
                        {% tag entityLink ? 'a' : 'picture' with {
                            class: 'associations__picture',
                            href: entityLink ?? null,
                            target: entityLink ? '_blank' : null
                        } %}

                            <span class="-vis-hidden">{{ entry.itemTitle }}</span>

                            {% set image = entry.imageSingular.one() ?? null %}
                            {% if image %}
                                {{ assetMacros.vectorSwitcher(image, 200, 200 ) }}
                            {% endif %}

                        {% endtag %}

                    </li>
                    {% endfor %}
                </ul>
                {% endif %}

                {% if smallLinks ?? false %}
                    <ul class="footer__technical-links">
                        {% include './01_core/_blocks/link-helper.twig' with { entry: smallLinks, itemParentElement : 'li' } %}
                    </ul>
                {% endif %}
                
                {% if smallLinks ?? false %}
                    <ul class="footer__technical-links">
                        {% include './01_core/_blocks/social-link.twig' with { socialLinksEntries: smallLinks.socialLinks.all(), itemParentElement : 'li' } %}
                    </ul>
                {% endif %}

            </div>
        </div>
    </div>



    <div class="footer__endcap">
        <div class="container">
            <div class="footer__columns">
                <div class="footer__column footer__column--logo">
                    <div class="footer__logo">Logo</div>
                </div>

                {% set quickLinks = craft.entries.section('footer').type('quicklinks').slug('quick-links').one() %}
                {% if quickLinks ?? false %}
                <div class="footer__column">
                    <h3>{{ quickLinks.itemTitle }}</h3>
                    {% if quickLinks.linkHelper|length %}
                        <ul class="footer__quicklinks__list">
                            {% include './01_core/_blocks/link-helper.twig' with { entry: quickLinks, itemParentElement : 'li' } %}
                        </ul>
                    {% endif %}
                </div>
                {% endif %}

                {% set locations = craft.entries.section('footer').type('locations').one() %}
                {% if locations ?? false %}
                <div class="footer__column">
                    <h3>{{ locations.title }}</h3>
                    {% if locations.locationName|length %}
                        <ul class="footer__quicklinks__list">
                            {% for location in locations.locationName.all() %}
                                <li>{{ location.title }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
                {% endif %}

                {% set stayConnected = craft.entries.section('footer').type('quicklinks').slug('stay-connected').one() %}
                {% if stayConnected ?? false %}
                <div class="footer__column">
                    <h3>{{ stayConnected.itemTitle }}</h3>
                    {% if stayConnected.linkHelper|length %}
                        <ul class="footer__quicklinks__list">
                            {% include './01_core/_blocks/link-helper.twig' with { entry: stayConnected, itemParentElement : 'li' } %}
                        </ul>
                    {% endif %}
                </div>
                {% endif %}

                <div class="footer__column footer__column--subscribe">
                    <h3>Subscribe</h3>
                    <p>Join our newsletter to stay updated on our latest features.</p>
                    {% set form = craft.formie.forms({ handle: 'enewsSignup' }).one() %}
                    {% include '02_components/form' with {
                        selectedForm: form,
                        'formComponent': {
                            modifierClass: 'footer__form',
                        },
                        styleCapsule: false
                    } %}
                    {% if enews.richtextBasicLinks ?? false %}
                        <p class="footer__footnote">{{ enews.richtextBasicLinks|striptags('<br><a><strong><b><emphasis><em>')|raw }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% include 'patterns/02-components/footer/website-attribution.twig' with {
        socialLinksEntries: smallLinks.socialLinks.all(),
        smallLinks: smallLinks
    } %}
</footer>
