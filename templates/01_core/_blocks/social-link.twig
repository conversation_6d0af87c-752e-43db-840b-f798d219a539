<!-- Template: {{ _self }} -->
{% import "01_core/_macros/macros-global.twig" as macros %}

{% set socialLinksEntries = socialLinksEntries ?? entry.socialLinks.all() ?? null %}

{% for block in socialLinksEntries %}

    {% set blockLink = block.linkField ?? null %}
    {% set blockIcon = block.iconAsset.one() ?? null %}
    {% set blockLinkStyle = block.linkStyle.value ?? null %}

    {% if blockLink ?? false %}

        {% if itemParentElement is defined %}
            <{{ itemParentElement }}>
        {% endif %}

        {% include '01_core/_blocks/logo-link' with {
            link: blockLink,
            linkWrapBlockElem: linkWrapBlockElem ?? null,
            icon: staticIcon is defined ? staticIcon : blockIcon,
            'individualLink': {
                modifierClass: blockLinkStyle|length ? blockLinkStyle : linkHelper.modifierClass ?? null,
            }
        } %}

        {% if itemParentElement is defined %}
            </{{ itemParentElement }}>
        {% endif %}

    {% endif %}

{% endfor %}

