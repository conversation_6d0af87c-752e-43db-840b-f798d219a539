{% extends "01_core/main" %}

{% set pageTitle = 'Home' %}

{% block meta %}
    {% set entry = craft.entries.section('home').type('meta').one() %}
    {{ parent() }}
{% endblock %}

{% set bodyAttributes = {
    class: 'home'
} %}

{% block banner %}
    {% include '02_components/_home/hero-banner' %}
{% endblock %}

{% block main_content %}

{% set homeContentEntries = craft.entries.section('home').type(['not', 'banner']).all() %}

{% for entry in homeContentEntries %}

    {% switch entry.type %}

        {% case "article" %}
        {% include '02_components/_home/article.twig' %}

        {% case "cardSet" %}
        <div class="section container">
            {% include '02_components/cardSet.twig' with { addOnEntry: entry } %}
        </div>

        {% case "postsGallery" %}
        <div class="section">
            <div class="container">
            {% include '02_components/postsGallery.twig' %}
            </div>
        </div>


        {% default %}


    {% endswitch %}

{% endfor %}

{% endblock %}



