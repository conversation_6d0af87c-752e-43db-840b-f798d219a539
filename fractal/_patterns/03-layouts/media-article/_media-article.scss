
@use "../../00-settings" as *;

.media-article {
    display: grid;
    gap: var(--section-small);

    img {
        width: 100%;
        height: auto;
        border-radius: 8px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: var(--paragraph-break);

        h2 {
            @include font-set('primary'); 
            font-weight: $fw-normal; 
        }
    }

    &__features {
        display: grid;
        gap: var(--paragraph-break);
        margin: var(--paragraph-break) 0;

        @include screen($bp560) {
            grid-template-columns: 1fr 1fr;
            gap: calc(var(--paragraph-break) * 1.5);
        }
    }

    &__feature {
        h3 {
            @include font-set('primary'); 
            font-weight: $fw-normal; 
            margin-bottom: 0.5em;
            font-size: 1.8rem;
            line-height: 1.3;
        }

        p {
            margin: 0;
            font-size: 1.6rem;
            line-height: 1.4;
        }
    }

    &__actions {
        display: flex;
        gap: 1.6rem;
        flex-wrap: wrap;
        margin-top: var(--paragraph-break);
    }
}

@include screen($bp768) {
    .media-article {
        grid-template-columns: 1fr 1fr;
        gap: var(--layout-column-gap);
        align-items: center;

        &--reverse {
            grid-template-columns: 1fr 1fr;

            .media-article__content {
                order: 1;
            }

            img {
                order: 2;
            }
        }
    }
}
