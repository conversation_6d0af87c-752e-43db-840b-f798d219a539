
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.footer {

    &__body {
        background: $color-white;
        @include reverse-text;

        &__container {
            display: grid;
            grid-template-columns: repeat(3, auto);
        }
    }

    &__endcap {
        background: $color-white;
        color: $color-black;
        padding-block: var(--section-small);
        
        &.container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
    }

    &__columns {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;

        @include screen($bp768) {
            grid-template-columns: repeat(2, 1fr);
        }

        @include screen($bp992) {
            grid-template-columns: repeat(4, 1fr) 2fr;
        }
    }

    &__column {
        h3 {
            margin-bottom: 1rem;
            font-size: 1.8rem;
            font-weight: $fw-normal;
        }

        &--logo {
            .footer__logo {
                svg {
                    width: auto;
                    height: 40px;
                }
            }
        }

        &--subscribe {
            p {
                margin-bottom: 1.5rem;
                font-size: 1.4rem;
                line-height: 1.4;
                color: $color-grey-07;
            }

            .footer__form {
                margin-bottom: 1rem;

                .fui-field {
                    display: flex;
                    gap: 0.5rem;
                    margin-bottom: 1rem;

                    input[type="email"] {
                        flex: 1;
                        padding: 0.8rem 1rem;
                        border: 1px solid $color-grey-04;
                        border-radius: 4px;
                        background: $color-grey-01;
                        color: $color-black;
                        font-size: 1.4rem;

                        &::placeholder {
                            color: $color-grey-06;
                        }
                    }

                    button {
                        padding: 0.8rem 1.5rem;
                        background: $color-black;
                        color: $color-white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-weight: $fw-normal;
                        font-size: 1.4rem;

                        &:hover {
                            background: $color-grey-08;
                        }
                    }
                }
            }

            .footer__footnote {
                font-size: 1.2rem;
                color: $color-grey-06;
                line-height: 1.3;
            }
        }
    }

    &__text {
        margin-top: 1em;
        
        .footer-header__tagline {
            font-family: var(--font-family-secondary);
            font-weight: 400;
            display: block;
            max-width: 600px;
            margin-bottom: var(--paragraph-break);
            color: $color-black;
            font-size: var(--h4-font-size);
            line-height: 1.6;
        }

        @include screen($bp992) {
            margin-top: 0;
        }

        .footer__btn {
            margin: 0;
            padding: 14px 28px;
            font-size: 1.5rem; 
            font-weight: 500;
            border-radius: 6px;
            text-decoration: none;
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            transition: all 0.3s ease;
            border: none;
            min-width: 140px; 
            line-height: 1; 
    
            &.button--alt {
                background: linear-gradient(135deg, $color-primary 0%, $color-secondary 100%);
                color: $color-white;
    
                &:hover {
                    background: linear-gradient(135deg, darken($color-primary, 10%) 0%, darken($color-secondary, 10%) 100%);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                }
            }
    
            &.button--neutral {
                background-color: transparent;
                color: $color-grey-08;
                border: 2px solid $color-grey-04;
    
                &:hover {
                    background-color: $color-grey-01;
                    border-color: $color-grey-06;
                }
            }
        }
    }

    &__quicklinks__list {
        @include list-reset;

        li {
            margin-bottom: 0.8rem;

            a {
                color: inherit;
                text-decoration: none;
                font-size: 1.4rem;
                line-height: 1.4;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }

    &__technical-links {
        @include list-reset;
        display: inline;

        li {
            @include screen($bp560) {
                display: inline;
            }

            &:after {
                @include screen($bp1600) {
                    display: inline-block;
                    width: 40px;
                }
            }

            a {
                text-decoration: none;
            }
        }
    }
}


.associations {
    @include list-reset;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: $spacing;

    img {
        max-width: 220px;
    }
}
