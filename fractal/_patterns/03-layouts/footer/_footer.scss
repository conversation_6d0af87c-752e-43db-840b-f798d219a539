
@use "../../00-settings" as *;
@use "../../01-base/typography/reverse-text-mixins" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;

.footer {

    &__body {
        background: $color-primary;
        @include reverse-text;

        &__container {
            display: grid;
            grid-template-columns: repeat(3, auto);
        }
    }

    &__endcap {
        background: $color-primary;
        @include reverse-text;
        padding-block: var(--section-small);
    }

    &__columns {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;

        @include screen($bp768) {
            grid-template-columns: repeat(2, 1fr);
        }

        @include screen($bp992) {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    &__column {
        h3 {
            margin-bottom: 1rem;
            font-size: 1.8rem;
            font-weight: $fw-normal;
        }

        &--logo {
            .footer__logo {
                font-size: 2.4rem;
                font-weight: $fw-bold;
                font-style: italic;
            }
        }

        &--subscribe {
            p {
                margin-bottom: 1rem;
                font-size: 1.4rem;
                line-height: 1.4;
            }

            .footer__form {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 1rem;

                input[type="email"] {
                    flex: 1;
                    padding: 0.8rem;
                    border: 1px solid $color-grey-04;
                    border-radius: 4px;
                    background: $color-white;
                    color: $color-black;
                }

                button {
                    padding: 0.8rem 1.5rem;
                    background: $color-secondary;
                    color: $color-white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: $fw-normal;

                    &:hover {
                        background: darken($color-secondary, 10%);
                    }
                }
            }

            .footer__footnote {
                font-size: 1.2rem;
                opacity: 0.8;
            }
        }
    }

    &__text {
        margin-top: 1em;

        @include screen($bp992) {
            margin-top: 0;
        }
    }

    &__technical-links {
        @include list-reset;
        display: inline;

        li {
            @include screen($bp560) {
                display: inline;
            }

            &:after {
                // content: "\2003";

                @include screen($bp1600) {
                    display: inline-block;
                    width: 40px;
                }
            }

            a {
                text-decoration: none;
            }
        }
    }
}


.associations {
    @include list-reset;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: $spacing;

    img {
        max-width: 220px;
    }
}
