@use "../../00-settings" as *;
@use "../../05-utilities/helpers/utility-mixins" as *;
@use "../../01-base/typography/_typography-mixins" as *;


.section-header {
    display: grid;
    text-align: center;
    margin-bottom: var(--section-small);
}

.section-header__subtitle {
    grid-row: 1;
    @include subtitle;
}

.section-header__title {
    @include font-set('primary');
    font-size: var(--h1-font-size);
    line-height: 1.2;
    color: $color-grey-08;
    margin-bottom: $spacing*3;
    font-weight: normal;

    @include screen($bp768) {
        font-size: 3.2rem;
    }
}

.section-header__cta {
    display: flex;
    gap: #{$spacing*2};
    align-items: center;
    justify-content: center;
    margin-top: $spacing*4;

    .button {
        @include font-set('primary');
        padding: #{$spacing*2} #{$spacing*4};
        font-size: var(--body-font-size);
        font-weight: 400;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.3s ease;
        min-width: 120px;

        &:first-child {
            background: transparent;
            color: $color-grey-08;
            border: 1px solid $color-grey-04;

            &:hover,
            &:focus,
            &:active {
                background: $color-grey-02;
                color: $color-grey-08;
                border-color: $color-grey-06;
            }
        }

        &:last-child {
            background: $color-grey-08;
            color: $color-white;
            border: 1px solid $color-grey-08;

            &:hover,
            &:focus,
            &:active {
                background: darken($color-grey-08, 10%);
                border-color: darken($color-grey-08, 10%);
            }
        }
    }
}

.section-header__tagline {
    @include font-set('primary');
    display: block;
    margin-inline: auto;
    max-width: 600px;
    margin-bottom: var(--paragraph-break);
    color: $color-grey-06;
    font-size: var(--body-font-size);
    line-height: 1.6;
}

.section-header--sidebar {
    text-align: left;
    margin-bottom: 0;
}
