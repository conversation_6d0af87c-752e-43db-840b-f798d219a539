@use "../../00-settings" as *;

/* Card Set Component Styles */
.card-set {
    padding: $spacing*10 0;

    .section-header {
        margin-bottom: $spacing*10;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .tidy-list-grid {
        margin-bottom: $spacing*8;
        gap: $spacing*6;

        @include screen($bp768) {
            gap: $spacing*8;
        }

        @include screen($bp1200) {
            gap: $spacing*10;
        }
    }
}

/* Services specific styling */
.card-set.bg-default {
    background: $color-white;
}
