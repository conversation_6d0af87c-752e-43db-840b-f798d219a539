
@use "../../00-settings" as *;
@use "../buttons-and-links/button-mixins" as *;

.button {
    @include button;
}

/* ---- Sizes ---- */
/* Small */
.button--small {
    @include button-size('small');
}

.button--y-small {
    --button-padding-vertical: 0.5em;
}

.button--large {
    @include button-size('large');
}


/* ---- Button Colours ---- */
.button--alt {
    @include button($color-primary);
}

.button--secondary {
    @include button($color-secondary);
}

.button--warning {
    @include button($color-utility-error-dark);
}

.button--neutral {
    @include button($color-grey-02, $color-grey-08);
}

.button--positive {
    @include button($color-utility-positive);
}


/* ---- Button Variations ---- */
.button--block {
    display: block;
    width: 100%;
}

.button--subtle {
    background-color: transparent;
    color: $color-grey-08;
    border: 2px solid $color-grey-04;
    font-size: 1.6rem;
    padding: 0.8em 1.5em;
    font-weight: $fw-normal; 

    svg {
        height: 1em;
        width: 1em;

        [stroke*="#"] {
            stroke: currentColor;
        }

        [fill*="#"] {
            fill: currentColor;
        }
    }

    &:hover,
    &:focus,
    &:active {
        background-color: $color-grey-08;
        border-color: $color-grey-08;
        color: $color-white;
    }
}

// Disabled/unavailable button style
.button--disabled {
    @include button-disabled;
}

/* Firefox: Get rid of the inner focus border */
.button::-moz-focus-inner {
    border: 0;
    padding: 0;
}
