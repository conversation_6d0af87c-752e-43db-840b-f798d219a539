

<section class="hero-banner hero-banner-01">
    <div class="container">
        <div class="hero-banner__content">
            <div class="row">
                <div class="col-xs-12 col-lg-6">
                    <div class="hero-banner__text">
                        {% if entry.itemTitle %}
                            <h1 class="hero-banner__heading">{{ entry.itemTitle }}</h1>
                        {% endif %}
                        {% if entry.bannerContent %}
                            <p class="hero-banner__description">{{ entry.bannerContent }}</p>
                        {% endif %}
                        <div class="hero-banner__cta">
                            <a href="#" class="button button--alt button--large hero-banner__btn">Learn More</a>
                            <a href="#" class="button button--neutral button--large hero-banner__btn">Sign Up</a>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="hero-banner__images-wrapper">
                        <div class="hero-banner__images">
                            <div class="hero-banner__images-column">
                                {% set multipleImages = entry.multipleImages.all() ?? [] %}
                                {% if multipleImages|length > 0 %}
                                    {% for image in multipleImages %}
                                        {% if loop.index is odd %}
                                            {% if fractal is not defined %}
                                                {% set imageUrl = image.getUrl({
                                                    mode: 'crop',
                                                    width: 400,
                                                    height: 300,
                                                    format: 'webp'
                                                }) %}
                                            {% else %}
                                                {% set imageUrl = multipleImages[loop.index0] ?? 'https://picsum.photos/400/300?random=' ~ loop.index %}
                                            {% endif %}
                                            <div class="hero-banner__image-item">
                                                <img src="{{ imageUrl }}" alt="" role="presentation" loading="lazy">
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    {# Fallback images for fractal/demo - first column (odd numbers) #}
                                    {% for i in [1, 3, 5] %}
                                        <div class="hero-banner__image-item">
                                            <img src="https://picsum.photos/400/300?random={{ i }}" alt="" role="presentation" loading="lazy">
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                            <div class="hero-banner__images-column">
                                {% if multipleImages|length > 0 %}
                                    {% for image in multipleImages %}
                                        {% if loop.index is even %}
                                            {% if fractal is not defined %}
                                                {% set imageUrl = image.getUrl({
                                                    mode: 'crop',
                                                    width: 400,
                                                    height: 300,
                                                    format: 'webp'
                                                }) %}
                                            {% else %}
                                                {% set imageUrl = multipleImages[loop.index0] ?? 'https://picsum.photos/400/300?random=' ~ loop.index %}
                                            {% endif %}
                                            <div class="hero-banner__image-item">
                                                <img src="{{ imageUrl }}" alt="" role="presentation" loading="lazy">
                                            </div>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    {# Fallback images for fractal/demo - second column (even numbers) #}
                                    {% for i in [2, 4, 6] %}
                                        <div class="hero-banner__image-item">
                                            <img src="https://picsum.photos/400/300?random={{ i }}" alt="" role="presentation" loading="lazy">
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

