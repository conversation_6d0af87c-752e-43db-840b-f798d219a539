
@use "../../00-settings" as *;

.hero-banner-01 {
    background: $color-alternative;
    background-color: $color-alternative;
    background-image: none;
    background-size: auto;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    color: $color-grey-08;
    position: static;

    // Remove any overlay
    &::before {
        display: none;
    }

    .hero-banner__content {
        width: 100%;
        display: flex;
        align-items: center;
        min-height: 100vh;
    }

    .hero-banner__text {
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-right: $spacing-large;
        flex: 1;

        @include screen($bp768) {
            padding-right: $spacing-xlarge;
        }
    }

    .hero-banner__images-wrapper {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-banner__heading {
        @include font-set('primary');
        font-size: 3.5rem; 
        line-height: 1.1;
        font-weight: 400; 
        color: $color-grey-08;
        margin-bottom: $spacing-medium;

        @include screen($bp768) {
            font-size: 4.5rem; 
        }

        @include screen($bp1200) {
            font-size: 5.5rem; 
        }
    }

    .hero-banner__description {
        @include font-set('primary');
        font-size: 1.25rem; 
        line-height: 1.5;
        color: $color-grey-07;
        margin-bottom: $spacing-large;
        max-width: 85%;

        @include screen($bp768) {
            font-size: 1.4rem; 
        }

        @include screen($bp1200) {
            font-size: 1.5rem; 
        }
    }

    .hero-banner__cta {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-medium;
        align-items: center;
    }

    .hero-banner__btn {
        margin: 0;
        padding: 14px 28px;
        font-size: 1.5rem; 
        font-weight: 500;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex; 
        align-items: center; 
        justify-content: center; 
        transition: all 0.3s ease;
        border: none;
        min-width: 140px; 
        line-height: 1; 

        &.button--alt {
            background: linear-gradient(135deg, $color-primary 0%, $color-secondary 100%);
            color: $color-white;

            &:hover {
                background: linear-gradient(135deg, darken($color-primary, 10%) 0%, darken($color-secondary, 10%) 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
        }

        &.button--neutral {
            background-color: transparent;
            color: $color-grey-08;
            border: 2px solid $color-grey-04;

            &:hover {
                background-color: $color-grey-01;
                border-color: $color-grey-06;
            }
        }
    }

    .hero-banner__images {
        display: flex;
        gap: 12px;
        max-width: 500px;
        height: 400px; 
        overflow: hidden; 
        position: relative;

        @include screen($bp768) {
            gap: 15px;
            max-width: 550px;
            height: 450px;
        }

        @include screen($bp1200) {
            gap: 18px;
            max-width: 800px;
            height: 100vh;
        }
    }

    // Column-based layout for staggered effect
    .hero-banner__images-column {
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex: 1;

        @include screen($bp768) {
            gap: 15px;
        }

        @include screen($bp1200) {
            gap: 18px;
        }

        // First column moves up
        &:first-child {
            transform: translateY(-150px);
        }

        // Second column moves down
        &:last-child {
            transform: translateY(0px);
        }
    }

    .hero-banner__image-item {
        border-radius: 12px;
        overflow: hidden;
        flex-shrink: 0;

        img {
            width: 100%;
            height: 160px;
            object-fit: cover;
            display: block;

            @include screen($bp768) {
                height: 180px;
            }

            @include screen($bp1200) {
                height: 350px;
            }
        }
    }
}

@include screen($bp768, 'max') {
    .hero-banner-01 {
        min-height: 100vh;

        .hero-banner__content {
            padding: $spacing-large 0;
            flex-direction: column;
            text-align: center;
        }

        .hero-banner__text {
            padding-right: 0;
            margin-bottom: $spacing-large;
            order: 1;
        }

        .hero-banner__images-wrapper {
            order: 2;
            margin-bottom: $spacing-large;
        }

        .hero-banner__heading {
            font-size: 2.5rem; 
        }

        .hero-banner__description {
            max-width: 100%;
            font-size: 1.1rem; 
        }

        .hero-banner__cta {
            justify-content: center;
            flex-direction: column;
            align-items: center;
            gap: $spacing-small;

            .hero-banner__btn {
                width: 200px;
                text-align: center;
            }
        }

        .hero-banner__images {
            max-width: 320px;
            height: 300px; 
            gap: 8px;
        }

        .hero-banner__images-column {
            gap: 8px;

            &:first-child {
                transform: translateY(-20px);
            }

            &:last-child {
                transform: translateY(20px);
            }
        }

        .hero-banner__image-item {
            img {
                height: 120px; 
            }
        }
    }
}
