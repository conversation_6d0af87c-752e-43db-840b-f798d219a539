
@use "../../00-settings" as *;
@use "../../05-utilities/helpers/_utility-mixins" as *;

a:where(.card) {
    text-decoration: none;
    color: inherit;
}

.card {

    --tb-padding: #{$spacing};
    --lr-padding: #{$spacing};

    @include screen($bp360) {
        --tb-padding: #{$spacing*2};
        --lr-padding: #{$spacing*2};
    }

    @include screen($bp560) {
        --tb-padding: #{$spacing*4};
        --lr-padding: #{$spacing*4};
    }

    background-color: $color-white;
    color: $color-body-font;
    display: flex;
    flex-direction: column;
    height: 100%;


    @container (width > 32ch) {
        --tb-padding: #{$spacing*2};
        --lr-padding: #{$spacing*2};
    }

    @container (width > 48ch) {
        --tb-padding: #{$spacing*4};
        --lr-padding: #{$spacing*4};
    }

    @container (width > 80ch) {
        flex-direction: row;
    }

    &[role="link"]:hover,
    &[role="link"]:focus,
    &[role="link"]:active {
        box-shadow:0 0 2px 0 rgba(0, 0, 0, 0.33)
    }
}

.card__heading {
    color: $color-secondary;
}

.card__content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    order: 1;
    padding: var(--tb-padding) var(--lr-padding);
    border-bottom-left-radius: $radius-default;
    border-bottom-right-radius: $radius-default;
    border: $border;
    border-top: 0;

}

.card__media {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $color-grey-01;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    border-top-left-radius: $radius-default;
    border-top-right-radius: $radius-default;
}

.card__cta {
    margin-top: auto;
}

.card__media--icon {
    display: grid;
    place-items: center;
}

.card__media__icon {
    width: 64px;
    height: 64px;
}

.card__media--fallback {
    @include fallback-logo-image;
}

.card__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Services card set specific styles */
.card--promo-set {
    border: none;
    background: transparent;
    text-align: center;
    transition: transform 0.3s ease;

    &:hover {
        transform: translateY(-4px);
    }

    .card__content {
        border: none;
        padding: $spacing*4 $spacing*2;
        order: 2;
    }

    .card__media {
        background: transparent;
        aspect-ratio: 1;
        width: 100px;
        height: 100px;
        margin: 0 auto;
        order: 1;
        border-radius: 0;

        &--icon {
            background: transparent; // Remove black background
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &--fallback {
            background: $color-black;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            &::before {
                content: "";
                width: 48px;
                height: 48px;
                background: $color-white;
                mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center;
                mask-size: contain;
            }
        }
    }

    // Specific service icons for different cards based on position
    &:nth-child(1) .card__media--fallback::before {
        mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 12h-4l-3 9L9 3l-3 9H2'/%3E%3C/svg%3E") no-repeat center;
    }

    &:nth-child(2) .card__media--fallback::before {
        mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6'/%3E%3C/svg%3E") no-repeat center;
    }

    &:nth-child(3) .card__media--fallback::before {
        mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2'/%3E%3Ccircle cx='9' cy='7' r='4'/%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75'/%3E%3C/svg%3E") no-repeat center;
    }

    .card__media__icon {
        width: 60px;
        height: 60px;
        // Remove filter: invert(1) to keep original icon colors
    }

    .card__heading {
        @include font-set('primary');
        color: $color-grey-08;
        font-size: var(--h4-font-size);
        margin-bottom: $spacing*3;
        font-weight: 600;
        line-height: 1.3;
    }

    p {
        @include font-set('primary');
        color: $color-grey-06;
        font-size: var(--body-font-size);
        line-height: 1.6;
        margin-bottom: 0;
    }
}

