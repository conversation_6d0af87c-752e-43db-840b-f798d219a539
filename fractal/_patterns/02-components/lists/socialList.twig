{% if socialArray is not defined %}
{% set socialArray = socialList.socialArray ?? ['facebook', 'instagram', 'youtube', 'linkedin', 'twitter', 'tiktok'] %}
{% endif %}

{% set socialFieldHasValue = false %}
{% for name in socialArray %}
    {% if siteIdentity[name]|length > 0 %}
        {% set socialFieldHasValue = true %}
    {% endif %}
{% endfor %}

{% if socialFieldHasValue %}
{% if socialList.title ?? false %}
<div class="social-list-wrapper"> 
{% endif %}
    <ul class="social-list {{ socialList.modifier ?? null }}">
    {% for item in socialArray %}
        {% include 'patterns/02-components/lists/socialListItem.twig' with { socialListItem: {
                name: item,
                link: siteIdentity[item]
            }
        } %}
    {% endfor %}
    </ul>
{% if socialList.title ?? false %}
</div>
{% endif %}
{% endif %}
